/**
 * Constantes de couleurs pour FloraSynth
 * Charte graphique unifiée pour maintenir la cohérence visuelle
 */

export const THEME_COLORS = {
  // Arrière-plans
  background: {
    primary: '#100f1c',      // Arrière-plan principal (violet très sombre)
    secondary: '#1c1a31',    // Conteneurs/<PERSON><PERSON> (violet légèrement plus clair)
    tertiary: '#2a2847',     // Éléments interactifs au survol
  },
  
  // Couleurs d'accent
  accent: {
    primary: '#d385f5',      // Couleur principale (gradient rose-lavande)
    hover: '#c070e0',        // État de survol
    light: '#e6a8ff',       // Version plus claire
    dark: '#b366d9',        // Version plus sombre
  },
  
  // Textes
  text: {
    primary: '#FFFFFF',      // Texte principal (blanc pur)
    secondary: '#E0E0E0',    // Texte secondaire (gris très clair)
    muted: '#9CA3AF',       // Texte atténué
    disabled: '#6B7280',    // Texte désactivé
  },
  
  // Couleurs de statut (adaptées au thème sombre)
  status: {
    success: {
      primary: '#10B981',    // Vert succès
      background: '#064E3B', // Fond vert sombre
      border: '#059669',     // Bordure vert moyen
    },
    warning: {
      primary: '#F59E0B',    // Orange avertissement
      background: '#78350F', // Fond orange sombre
      border: '#D97706',     // Bordure orange moyen
    },
    error: {
      primary: '#EF4444',    // Rouge erreur
      background: '#7F1D1D', // Fond rouge sombre
      border: '#DC2626',     // Bordure rouge moyen
    },
    info: {
      primary: '#3B82F6',    // Bleu information
      background: '#1E3A8A', // Fond bleu sombre
      border: '#2563EB',     // Bordure bleu moyen
    },
  },
  
  // Couleurs de priorité pour notifications (conformes à la charte FloraSynth)
  priority: {
    urgent: {
      background: '#7F1D1D', // Rouge sombre
      border: '#EF4444',     // Rouge vif
      text: '#FEE2E2',       // Rouge très clair
    },
    high: {
      background: '#2a2847', // Violet sombre (charte FloraSynth)
      border: '#d385f5',     // Violet principal (charte FloraSynth)
      text: '#E6A8FF',       // Violet très clair (charte FloraSynth)
    },
    medium: {
      background: '#1c1a31', // Violet plus sombre (charte FloraSynth)
      border: '#a364f7',     // Violet secondaire (charte FloraSynth)
      text: '#D1C4E9',       // Violet clair (charte FloraSynth)
    },
    low: {
      background: '#064E3B', // Vert sombre
      border: '#10B981',     // Vert vif
      text: '#D1FAE5',       // Vert très clair
    },
  },
  
  // Bordures et séparateurs
  border: {
    primary: '#374151',     // Bordure principale
    secondary: '#4B5563',   // Bordure secondaire
    accent: '#d385f5',      // Bordure d'accent
    muted: '#6B7280',      // Bordure atténuée
  },
  
  // États interactifs
  interactive: {
    hover: '#2a2847',       // Survol
    active: '#3a3660',      // Actif
    focus: '#d385f5',       // Focus
    disabled: '#1F2937',    // Désactivé
  },
} as const;

/**
 * Classes Tailwind CSS correspondantes pour utilisation directe
 */
export const THEME_CLASSES = {
  // Arrière-plans
  bg: {
    primary: 'bg-[#100f1c]',
    secondary: 'bg-[#1c1a31]',
    tertiary: 'bg-[#2a2847]',
  },
  
  // Textes
  text: {
    primary: 'text-white',
    secondary: 'text-[#E0E0E0]',
    muted: 'text-gray-400',
    disabled: 'text-gray-500',
  },
  
  // Accents
  accent: {
    primary: 'text-[#d385f5]',
    bg: 'bg-[#d385f5]',
    border: 'border-[#d385f5]',
    hover: 'hover:bg-[#c070e0]',
  },
  
  // Bordures
  border: {
    primary: 'border-gray-600',
    accent: 'border-[#d385f5]',
    muted: 'border-gray-500',
  },
} as const;

/**
 * Fonction utilitaire pour obtenir les couleurs de priorité
 */
export const getPriorityColors = (priority: 'urgent' | 'high' | 'medium' | 'low') => {
  return THEME_COLORS.priority[priority];
};

/**
 * Fonction utilitaire pour obtenir les couleurs de statut
 */
export const getStatusColors = (status: 'success' | 'warning' | 'error' | 'info') => {
  return THEME_COLORS.status[status];
};
